import { Layout } from "antd";
import { Header } from "./components/Header";
import { <PERSON> } from "./components/Hero";
import { StatsPanel } from "./components/StatsPanel";
import { LockForm } from "./components/LockForm";
import { AccountOverview } from "./components/AccountOverview";
import { Footer } from "./components/Footer";
import "./App.css";
import * as buffer from "buffer";
import { DonationList } from "./components/DonationList";
import { NewsPanel } from "./components/NewsPanel";
window.Buffer = buffer.Buffer;

const App: React.FC = () => {
  return (
    <Layout className="app-layout">
      <Header />
      <div style={{ width: "100%", height: "100%", background: "rgba(7, 8, 28, 0.35)" }}>
        <Hero />
        <NewsPanel />
        <StatsPanel />
        <DonationList />
      </div>
      {/* <StatsPanel />
        <LockForm />
        <AccountOverview /> */}
      <Footer />
    </Layout>
  );
};

export default App;
