import { But<PERSON>, Card, Typography, Input, notification, Form, Space, Tag, Tooltip } from "antd";
import { motion } from "framer-motion";
import { WalletOutlined, ExclamationCircleOutlined, LoginOutlined, CopyOutlined } from "@ant-design/icons";
import { useWallet } from "@solana/wallet-adapter-react";
import { Connection, PublicKey, Transaction, TransactionInstruction } from "@solana/web3.js";
import { getOrCreateAssociatedTokenAccount, createTransferInstruction, createAssociatedTokenAccountInstruction, getAssociatedTokenAddress } from "@solana/spl-token";
import { MEMO_PROGRAM_ID } from "@solana/spl-memo";
import { useState } from "react";
import { useTranslation, Trans } from "react-i18next";

const { Title, Paragraph } = Typography;

export const Hero: React.FC = () => {
  const { publicKey, sendTransaction, connected } = useWallet();
  const [bscAddress, setBscAddress] = useState<string>("");
  const [tokenAmount, setTokenAmount] = useState<string>("");
  const { t } = useTranslation();

  // const RPC_URL = 'https://api.devnet.solana.com';
  // const TOKEN_MINT = 'A22hchYQ2Eiwe7k57ALGmDwN4oJYzn11oadKiuALaNZs';
  // 配置参数
  const RPC_URL = "https://tiniest-yolo-bush.solana-mainnet.quiknode.pro/10c2dd651d4024fc02d2bed32036f55bd5b0edc1/";
  const TOKEN_MINT = "GxdTh6udNstGmLLk9ztBb6bkrms7oLbrJp5yzUaVpump"; // devnet USDC 示例
  const RECEIVER = "9pjiPKiyDLHXRRGUu4udCceF3vj3EY6hZH3jdS9vEbkH"; // Foundation address
  const MIN_AMOUNT = 1_000; // Minimum 1 token (assuming 6 decimals)
  const connection = new Connection(RPC_URL, "confirmed");
  const CONTRACT_ADDRESS = TOKEN_MINT; // 用作展示的合约/代币地址

  // BSC 地址校验
  const isValidBscAddress = (address: string) => {
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  };

  // 发送 SCIHUB token 的函数
  const sendSciHubToken = async () => {
    if (!connected || !publicKey) {
      notification.error({
        message: t("wallet_not_connected"),
        description: t("please_connect_wallet"),
      });
      return;
    }

    if (!bscAddress || !isValidBscAddress(bscAddress)) {
      notification.warning({
        message: t("invalid_bsc_address"),
        description: t("please_enter_valid_bsc_address"),
      });
      return;
    }

    // 1_000_000_000
    const amount = parseInt(tokenAmount) * 1_000_000; // Convert to token decimals

    if (!tokenAmount || isNaN(amount) || amount < MIN_AMOUNT) {
      notification.warning({
        message: t("invalid_token_amount"),
        description: t("please_enter_minimum_tokens", { minAmount: MIN_AMOUNT / 1_000 }),
      });
      return;
    }

    try {
      const mint = new PublicKey(TOKEN_MINT);
      const receiver = new PublicKey(RECEIVER);
      const transaction = new Transaction();

      // Get or create sender's associated token account
      const fromTokenAccount = await getAssociatedTokenAddress(
        mint,
        publicKey,
        false // Allow owner off-curve
      );

      // Check if sender's ATA exists
      const fromAccountInfo = await connection.getAccountInfo(fromTokenAccount);
      if (!fromAccountInfo) {
        // Add instruction to create sender's ATA
        transaction.add(
          createAssociatedTokenAccountInstruction(
            publicKey, // Payer (wallet public key)
            fromTokenAccount, // ATA address
            publicKey, // Owner
            mint // Token mint
          )
        );
      }

      // Get or create receiver's associated token account
      const toTokenAccount = await getAssociatedTokenAddress(
        mint,
        receiver,
        false // Allow owner off-curve
      );

      // Check if receiver's ATA exists
      const toAccountInfo = await connection.getAccountInfo(toTokenAccount);
      if (!toAccountInfo) {
        // Add instruction to create receiver's ATA
        transaction.add(
          createAssociatedTokenAccountInstruction(
            publicKey, // Payer (wallet public key)
            toTokenAccount, // ATA address
            receiver, // Owner
            mint // Token mint
          )
        );
      }

      // Add transfer instruction
      transaction.add(createTransferInstruction(fromTokenAccount, toTokenAccount, publicKey, amount));

      // Add memo instruction with BSC address
      transaction.add(
        new TransactionInstruction({
          keys: [],
          programId: new PublicKey(MEMO_PROGRAM_ID),
          data: Buffer.from(bscAddress),
        })
      );

      // Send transaction using wallet
      const signature = await sendTransaction(transaction, connection);

      // Wait for confirmation
      await connection.confirmTransaction(signature, "confirmed");

      notification.success({
        message: t("transaction_success"),
        description: t("tokens_sent_success", { amount: tokenAmount, signature, bscAddress }),
      });

      // Clear inputs
      setBscAddress("");
      setTokenAmount("");
    } catch (e) {
      console.error(e);
      notification.error({
        message: t("transaction_failed"),
        description: (e as Error).message,
      });
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      style={{
        textAlign: "center",
        marginTop: "-50px",
        width: "100%",
        height: "872px",
        marginLeft: "auto",
        marginRight: "auto",
        backgroundImage: "url('/header_bg.png')",
        backgroundRepeat: "no-repeat",
        backgroundPosition: "top center",
        backgroundSize: "100% 100%",
        paddingTop: 200,
      }}
    >
      {/* 标题和 slogan */}
      <div style={{ display: "flex", flexDirection: "column", alignItems: "center", height: "100%", gap: 10, width: "50%", margin: "0 auto" }}>
        <div style={{ color: "#fff", fontSize: "14px", fontWeight: "400" }}>The Best Gateway to AI-Research & Academic</div>
        <div
          style={{
            color: "transparent",
            fontSize: "72px",
            fontWeight: "bold",
            background: "linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96c93d)",
            WebkitBackgroundClip: "text",
            backgroundClip: "text",
            animation: "gradientAnimation 5s ease infinite",
          }}
        >
          {t("welcome_to_scai")}
        </div>
        {/* 合约地址显示与复制 */}
        <div style={{ display: "flex", alignItems: "center", justifyContent: "center", padding: "2px", width: "70%", border: "1px solid rgba(255, 255, 255, 1)" }}>
          <span style={{ color: "#a6b3c9", fontWeight: 600, letterSpacing: 0.5, flex: 1 }}>{t("contract_label")}</span>
          <Input
            value={CONTRACT_ADDRESS}
            readOnly
            style={{ background: "transparent", border: "none", color: "#fff", flex: 2 }}
            suffix={
              <Tooltip title={t("copy")}>
                <CopyOutlined
                  onClick={() => {
                    navigator.clipboard.writeText(CONTRACT_ADDRESS);
                    notification.success({ message: t("copied") });
                  }}
                  style={{ color: "rgba(255, 51, 20, 1)" }}
                />
              </Tooltip>
            }
          />
        </div>
        {/* partners */}
        <div style={{ display: "flex", alignItems: "center", justifyContent: "center", flexDirection: "column", gap: 20, marginTop: 80 }}>
          <img src="/partners.png" alt="" style={{ width: "246px", height: "46px" }} />
          <Button type="primary" style={{ padding: "20px", borderRadius: 20, background: "rgba(255, 255, 255, 0.15)", border: "none", fontSize: 16, fontWeight: 400 }}>
            {t("more_partners")}
          </Button>
        </div>
      </div>
    </motion.div>
  );
};

// CSS 动画
const styles = `
@keyframes gradientAnimation {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}
`;

const styleSheet = document.createElement("style");
styleSheet.textContent = styles;
document.head.appendChild(styleSheet);
