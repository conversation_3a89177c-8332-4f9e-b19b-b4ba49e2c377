import { useState, useEffect, useMemo } from 'react';
import { Button, Card, Form, InputNumber, message, Spin, Statistic } from 'antd';
import { Program, BN, AnchorProvider, Wallet } from '@coral-xyz/anchor';
import { PublicKey, SystemProgram, Transaction } from '@solana/web3.js';
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { TOKEN_PROGRAM_ID, ASSOCIATED_TOKEN_PROGRAM_ID, getAssociatedTokenAddress, getAccount, createAssociatedTokenAccountInstruction } from '@solana/spl-token';
import { ScihubLock } from '../types/scihub_lock';
import idl from '../types/scihub.json';
import { motion } from 'framer-motion';
import * as anchor from '@coral-xyz/anchor';
import { Buffer as BufferPolyfill } from 'buffer';
import { LockOutlined, GiftOutlined } from '@ant-design/icons';

// Polyfill Buffer for browser environments
globalThis.Buffer = BufferPolyfill;

// ===================== Configuration Constants =====================
const PROGRAM_ID = new PublicKey('HqmtqTCGNjN7KvcMkKPEzVqLYwReXCSL2QhFvUsdse33');
const TOKEN_MINT = new PublicKey('A22hchYQ2Eiwe7k57ALGmDwN4oJYzn11oadKiuALaNZs');
const TOKEN_DECIMALS = 9;

// ===================== LockForm Component =====================
export const LockForm: React.FC = () => {
    const { connection } = useConnection();
    const wallet = useWallet();
    const { publicKey, sendTransaction } = wallet;
    const [form] = Form.useForm();
    const [loading, setLoading] = useState(false);
    const [totalLockedAmount, setTotalLockedAmount] = useState<number | null>(null);
    const [fetchingStats, setFetchingStats] = useState(true);

    const provider = useMemo(() => {
        if (!connection || !wallet || !wallet.publicKey) {
            return null;
        }
        return new AnchorProvider(connection, wallet as Wallet, AnchorProvider.defaultOptions());
    }, [connection, wallet]);

    const onLock = async (values: { amount: number; duration: number }) => {
        if (!wallet.publicKey || !provider) {
            message.error('请连接您的钱包并确保 Solana 连接已准备好。');
            return;
        }
        setLoading(true);
        try {
            const [projectLockPDA] = PublicKey.findProgramAddressSync(
                [Buffer.from('project_lock'), TOKEN_MINT.toBuffer()],
                PROGRAM_ID
            );

            const program = new Program<ScihubLock>(idl, provider);

            console.log('p: ==' + projectLockPDA.toBase58());
            console.log(TOKEN_MINT.toBase58());
            console.log(program.programId.toBase58());

            const lockTokenAccount = await PublicKey.findProgramAddressSync(
                [projectLockPDA.toBuffer(), TOKEN_PROGRAM_ID.toBuffer(), TOKEN_MINT.toBuffer()],
                ASSOCIATED_TOKEN_PROGRAM_ID
            );

            console.log(projectLockPDA.toBase58());
            console.log(lockTokenAccount[0].toBase58());

            let tokenAccount;
            tokenAccount = await getAccount(provider.connection, lockTokenAccount[0]);

            const currentTime = Math.floor(Date.now() / 1000);
            const userTokenAccount = await getAssociatedTokenAddress(TOKEN_MINT, wallet.publicKey);

            const [userLockInfo] = PublicKey.findProgramAddressSync(
                [Buffer.from('user_lock_info'), wallet.publicKey.toBuffer(), projectLockPDA.toBuffer()],
                PROGRAM_ID
            );

            console.log(userLockInfo.toBase58());

            try {
                const tx_b = await program.account.userLockInfo.fetch(userLockInfo);
                console.log(tx_b);
            } catch {
                console.log('create account');
                const tx_b = await program.methods
                    .initUserLockInfo()
                    .accounts({
                        tokenMint: TOKEN_MINT,
                    })
                    .rpc();
                console.log('ProjectLock initialized:', tx_b);
            }

            const tx = await program.methods
                .lock(new BN(values.amount * 10 ** TOKEN_DECIMALS), new BN(currentTime + values.duration))
                .accounts({
                    user: wallet.publicKey,
                    tokenMint: TOKEN_MINT,
                    userTokenAccount: userTokenAccount,
                    lockTokenAccount: tokenAccount.address,
                })
                .preInstructions([anchor.web3.ComputeBudgetProgram.setComputeUnitLimit({ units: 400_000 })])
                .rpc();

            message.success(`成功质押 ${values.amount} 个代币！交易 ID: ${tx}`);
            form.resetFields();
        } catch (error) {
            message.error('质押失败: ' + (error as Error).message);
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    const onDonate = async (values: { amount: number }) => {
        if (!wallet.publicKey || !provider) {
            message.error('请连接您的钱包并确保 Solana 连接已准备好。');
            return;
        }
        setLoading(true);
        try {
            const [projectLockPDA] = PublicKey.findProgramAddressSync(
                [Buffer.from('project_lock'), TOKEN_MINT.toBuffer()],
                PROGRAM_ID
            );

            const program = new Program<ScihubLock>(idl, provider);

            console.log('p: ==' + projectLockPDA.toBase58());
            console.log(TOKEN_MINT.toBase58());
            console.log(program.programId.toBase58());

            const lockTokenAccount = await PublicKey.findProgramAddressSync(
                [projectLockPDA.toBuffer(), TOKEN_PROGRAM_ID.toBuffer(), TOKEN_MINT.toBuffer()],
                ASSOCIATED_TOKEN_PROGRAM_ID
            );

            console.log(projectLockPDA.toBase58());
            console.log(lockTokenAccount[0].toBase58());

            let tokenAccount;
            tokenAccount = await getAccount(provider.connection, lockTokenAccount[0]);

            const currentTime = Math.floor(Date.now() / 1000);
            const userTokenAccount = await getAssociatedTokenAddress(TOKEN_MINT, wallet.publicKey);

            const [userLockInfo] = PublicKey.findProgramAddressSync(
                [Buffer.from('user_lock_info'), wallet.publicKey.toBuffer(), projectLockPDA.toBuffer()],
                PROGRAM_ID
            );

            console.log(userLockInfo.toBase58());

            try {
                const tx_b = await program.account.userLockInfo.fetch(userLockInfo);
                console.log(tx_b);
            } catch {
                console.log('create account');
                const tx_b = await program.methods
                    .initUserLockInfo()
                    .accounts({
                        tokenMint: TOKEN_MINT,
                    })
                    .rpc();
                console.log('ProjectLock initialized:', tx_b);
            }

            const tx = await program.methods
                .donation(new BN(values.amount * 10 ** TOKEN_DECIMALS))
                .accounts({
                    user: wallet.publicKey,
                    tokenMint: TOKEN_MINT,
                    userTokenAccount: userTokenAccount,
                    lockTokenAccount: tokenAccount.address,
                })
                .rpc();

            message.success(`成功捐赠 ${values.amount} 个代币！交易 ID: ${tx}`);
        } catch (error) {
            message.error('捐赠失败: ' + (error as Error).message);
            console.error(error);
        } finally {
            setLoading(false);
        }
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            id="lock-form"
            style={{ maxWidth: 800, margin: '0 auto' }}
        >
            <Card
                title={
                    <span style={{ color: '#fff', fontSize: '24px', fontWeight: 'bold' }}>
                        <LockOutlined style={{ marginRight: 8, color: '#40c4ff' }} />
                        Staking and Donation
                    </span>
                }
                style={{
                    marginBottom: 24,
                    background: 'linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%)',
                    borderRadius: 8,
                    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
                    border: 'none',
                }}
                headStyle={{ background: 'transparent', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}
            >
                <Form
                    form={form}
                    layout="vertical"
                    onFinish={onLock}
                    style={{ color: '#fff', maxWidth: 600, margin: '0 auto' }}
                >
                    <Form.Item
                        name="amount"
                        label={<span style={{ color: '#fff' }}>Staking Amount (SCAI Token)</span>}
                        rules={[{ required: true, message: 'Please enter the number of tokens to stake' }]}
                    >
                        <InputNumber
                            min={0}
                            style={{
                                width: '100%',
                                background: 'rgba(255, 255, 255, 0.05)',
                                color: '#fff',
                                border: '1px solid rgba(255, 255, 255, 0.2)',
                                borderRadius: 4,
                            }}
                            step={0.01}
                            placeholder="Enter amount"
                        />
                    </Form.Item>
                    <Form.Item
                        name="duration"
                        label={<span style={{ color: '#fff' }}>Lock Duration (Days)</span>}
                        rules={[{ required: true, message: 'Please enter the number of lock days' }]}
                    >
                        <InputNumber
                            min={60}
                            style={{
                                width: '100%',
                                background: 'rgba(255, 255, 255, 0.05)',
                                color: '#fff',
                                border: '1px solid rgba(255, 255, 255, 0.2)',
                                borderRadius: 4,
                            }}
                            placeholder="Enter duration"
                        />
                    </Form.Item>
                    <Form.Item>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={loading}
                            block
                            style={{
                                background: 'linear-gradient(45deg, #1890ff, #40c4ff)',
                                border: 'none',
                                borderRadius: 4,
                                height: 48,
                                fontSize: '16px',
                                transition: 'all 0.3s',
                            }}
                            onMouseEnter={(e) => (e.currentTarget.style.transform = 'scale(1.05)')}
                            onMouseLeave={(e) => (e.currentTarget.style.transform = 'scale(1)')}
                        >
                            Stake Tokens
                        </Button>
                    </Form.Item>
                </Form>

                <div
                    style={{
                        borderBottom: '1px solid rgba(255, 255, 255, 0.1)',
                        margin: '24px 0',
                    }}
                />

                <Form
                    layout="vertical"
                    onFinish={onDonate}
                    style={{ color: '#fff', maxWidth: 600, margin: '0 auto' }}
                >
                    <Form.Item
                        name="amount"
                        label={
                            <span style={{ color: '#fff' }}>
                                <GiftOutlined style={{ marginRight: 8, color: '#40c4ff' }} />
                                Donate Amount (SCAI Token)
                            </span>
                        }
                        rules={[{ required: true, message: 'Please enter the number of tokens to donate' }]}
                    >
                        <InputNumber
                            min={0}
                            style={{
                                width: '100%',
                                background: 'rgba(255, 255, 255, 0.05)',
                                color: '#fff',
                                border: '1px solid rgba(255, 255, 255, 0.2)',
                                borderRadius: 4,
                            }}
                            step={0.01}
                            placeholder="Enter amount"
                        />
                    </Form.Item>
                    <Form.Item>
                        <Button
                            type="primary"
                            htmlType="submit"
                            loading={loading}
                            block
                            style={{
                                background: 'linear-gradient(45deg, #ff4d4f, #ff7875)',
                                border: 'none',
                                borderRadius: 4,
                                height: 48,
                                fontSize: '16px',
                                transition: 'all 0.3s',
                            }}
                            onMouseEnter={(e) => (e.currentTarget.style.transform = 'scale(1.05)')}
                            onMouseLeave={(e) => (e.currentTarget.style.transform = 'scale(1)')}
                        >
                            Donate
                        </Button>
                    </Form.Item>
                </Form>
            </Card>
        </motion.div>
    );
};