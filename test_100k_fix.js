// 测试10万卡住问题的修复
// 在浏览器控制台中运行

console.log('🔧 测试10万卡住问题的修复...');

// 1. 清除当前缓存
function clearAndTest() {
    console.log('🗑️  清除当前缓存...');
    localStorage.removeItem('irys_stats_cache');

    console.log('🔄 3秒后刷新页面开始测试...');
    setTimeout(() => {
        window.location.reload();
    }, 3000);
}

// 2. 检查当前进度
function checkProgress() {
    const cached = localStorage.getItem('irys_stats_cache');
    if (cached) {
        try {
            const data = JSON.parse(cached);
            console.log(`📊 当前进度: ${data.totalCount} DOIs`);
            console.log(`📊 最后更新: ${new Date(data.lastUpdate).toLocaleString()}`);
            console.log(`📊 游标: ${data.lastCursor ? data.lastCursor.substring(0, 30) + '...' : 'null'}`);

            if (data.totalCount >= 100000 && data.totalCount < 150000) {
                console.log('⚠️  检测到可能卡在10万附近');
                console.log('💡 建议运行 clearAndTest() 重新测试');
            } else if (data.totalCount >= 200000) {
                console.log('✅ 数据看起来正常，已超过20万');
            }
        } catch (e) {
            console.log('❌ 缓存解析失败:', e);
        }
    } else {
        console.log('📊 无缓存数据');
    }
}

// 3. 监控实时进度
function monitorProgress() {
    console.log('👀 开始监控进度...');
    const interval = setInterval(() => {
        const cached = localStorage.getItem('irys_stats_cache');
        if (cached) {
            try {
                const data = JSON.parse(cached);
                console.log(`📊 实时进度: ${data.totalCount} DOIs`);

                if (data.totalCount >= 200000) {
                    console.log('🎉 成功突破20万！停止监控');
                    clearInterval(interval);
                }
            } catch (e) {
                // 忽略解析错误
            }
        }
    }, 10000); // 每10秒检查一次

    // 5分钟后自动停止监控
    setTimeout(() => {
        clearInterval(interval);
        console.log('⏰ 监控超时，已停止');
    }, 300000);

    return interval;
}

// 暴露函数
window.clearAndTest = clearAndTest;
window.checkProgress = checkProgress;
window.monitorProgress = monitorProgress;

// 自动检查当前状态
console.log('\n=== 当前状态检查 ===');
checkProgress();

console.log('\n=== 可用命令 ===');
console.log('clearAndTest() - 清除缓存并重新测试');
console.log('checkProgress() - 检查当前进度');
console.log('monitorProgress() - 开始监控进度');

console.log('\n=== 修复说明 ===');
console.log('✅ 停止条件已优化：连续3个小页面才停止');
console.log('✅ 最大页数已大幅增加：50 → 1000页');
console.log('✅ 更智能的结束检测：基于页面大小和游标');
console.log('✅ 详细的调试日志：显示小页面检测');
console.log('✅ 进度汇总：每50页显示一次总进度');

console.log('\n🔧 测试准备完成！');
