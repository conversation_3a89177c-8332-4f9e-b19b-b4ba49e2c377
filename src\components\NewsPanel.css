/* Scrolling Banner Styles */
.scrolling-banner-container {
  width: 100%;
  height: 60px;
  overflow: hidden;
  display: flex;
  align-items: center;
  position: relative;
}

.scrolling-banner {
  display: flex;
  align-items: center;
  height: 100%;
  animation: scroll-left 30s linear infinite;
  white-space: nowrap;
}

.scrolling-text {
  color: #fff;
  font-size: 18px;
  font-weight: 600;
  letter-spacing: 3px;
  text-transform: uppercase;
}

@keyframes scroll-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .scrolling-banner-container {
    height: 50px;
  }

  .scrolling-text {
    font-size: 16px;
    letter-spacing: 2px;
  }

  .scrolling-banner {
    animation: scroll-left 20s linear infinite;
  }

  /* News Cards Responsive */
  .news-cards-container {
    flex-direction: column !important;
    gap: 16px !important;
  }

  .news-card {
    width: 100% !important;
    max-width: 500px !important;
  }

  .news-arrow {
    display: none !important;
  }
}

@media (max-width: 480px) {
  .scrolling-banner-container {
    height: 40px;
  }

  .scrolling-text {
    font-size: 14px;
    letter-spacing: 1px;
  }

  .scrolling-banner {
    animation: scroll-left 15s linear infinite;
  }

  .news-card {
    min-height: 180px !important;
  }

  .news-card-title {
    font-size: 18px !important;
  }

  .news-card-content {
    font-size: 13px !important;
  }
}

/* Tablet specific styles */
@media (min-width: 481px) and (max-width: 768px) {
  .news-cards-container {
    flex-direction: column !important;
  }

  .news-card {
    width: 100% !important;
    max-width: 600px !important;
  }
}

/* Desktop styles */
@media (min-width: 769px) {
  .news-cards-container {
    flex-direction: row !important;
    justify-content: center !important;
  }

  .news-card {
    width: 400px !important;
  }
}

/* Hover effects for better interactivity */
.scrolling-banner-container:hover .scrolling-banner {
  animation-play-state: paused;
}

/* Smooth fade edges for better visual effect */
.scrolling-banner-container::before,
.scrolling-banner-container::after {
  content: "";
  position: absolute;
  top: 0;
  width: 50px;
  height: 100%;
  z-index: 2;
  pointer-events: none;
}

.scrolling-banner-container::before {
  left: 0;
  background: linear-gradient(to right, rgba(7, 8, 28, 0.35) 0%, transparent 100%);
}

.scrolling-banner-container::after {
  right: 0;
  background: linear-gradient(to left, rgba(7, 8, 28, 0.35) 0%, transparent 100%);
}
