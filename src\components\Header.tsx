import { Button, Layout, Typography, Select, Drawer, <PERSON>u, Space } from "antd";
import { Wallet } from "./wallet";
import { useWallet } from "@solana/wallet-adapter-react";
import { motion } from "framer-motion";
import { WalletOutlined, MenuOutlined } from "@ant-design/icons";
import { useTranslation } from "react-i18next";
import { useState } from "react";
import "./Header.css";
import "../i18n";

const { Header: AntHeader } = Layout;
const { Title } = Typography;

export const Header: React.FC = () => {
  const { publicKey } = useWallet();
  const { i18n, t } = useTranslation();
  const [drawerVisible, setDrawerVisible] = useState(false); // 控制抽屉显示

  const handleLanguageChange = (value: string) => {
    i18n.changeLanguage(value);
  };

  const toggleDrawer = () => {
    setDrawerVisible(!drawerVisible);
  };

  // 汉堡菜单内容
  const menuItems = (
    <Menu
      style={{ border: "none", background: "transparent" }}
      items={[
        {
          key: "links",
          label: (
            <Space direction="vertical" style={{ width: "100%" }}>
              <a href="https://scai.foundation" target="_blank" rel="noreferrer" style={{ color: "#fff" }}>
                {t("official_website")}
              </a>
              <a href="https://scai.foundation/app" target="_blank" rel="noreferrer" style={{ color: "#fff" }}>
                {t("app")}
              </a>
              <a href="https://t.me/WTFDeSci" target="_blank" rel="noreferrer" style={{ color: "#fff" }}>
                {t("community")}
              </a>
            </Space>
          ),
        },
        {
          key: "language",
          label: (
            <Select
              defaultValue={i18n.language}
              style={{ width: "100%" }}
              onChange={handleLanguageChange}
              options={[
                { value: "en", label: "English" },
                { value: "zh", label: "中文" },
              ]}
            />
          ),
        },
        {
          key: "wallet",
          label: <Wallet />,
        },
      ]}
    />
  );

  return (
    <AntHeader
      style={{
        background: "rgba(7, 8, 28, 0.35)",
        padding: "0 16px",
        display: "flex",
        flexDirection: "row",
        alignItems: "center",
        justifyContent: "space-between",
        backdropFilter: "blur(24px)",
        boxShadow: "0 2px 4px rgba(0, 0, 0, 0.2)",
        border: "1px solid rgba(255, 255, 255, 0.14)",
        position: "sticky",
        top: 0,
        zIndex: 1000,
      }}
      className="header"
    >
      <motion.div initial={{ opacity: 0, y: -20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.3 }} style={{ display: "flex", alignItems: "center", gap: 12, justifyContent: "space-between", width: "100%" }}>
        <div className="header-logo" onClick={() => window.open("/", "_blank")}>
          <img
            src="/logo.png"
            alt="SCAI Foundation"
            style={{
              height: "32px",
              marginRight: "8px",
              borderRadius: "50%",
              boxShadow: "0 2px 4px rgba(0, 0, 0, 0.3)",
            }}
          />
          <Title style={{ margin: 0, color: "#fff", fontSize: "22px", fontWeight: "bold" }}>SCAI</Title>
          <div style={{ width: 1, height: 20, backgroundColor: "#fff", margin: "0 12px" }}></div>
          <Title
            level={1}
            style={{
              margin: 0,
              color: "#fff",
              fontSize: "20px",
              fontWeight: "300",
              display: "flex",
              alignItems: "center",
            }}
          >
            {t("title")}
          </Title>
        </div>

        {/* 桌面端导航链接 */}
        <div className="desktop-only" style={{ display: "flex", alignItems: "center", gap: 50 }}>
          <a href="https://scai.sh" target="_blank" rel="noreferrer" style={{ color: "#fff", opacity: 0.9, fontSize: "16px", fontWeight: "800" }}>
            {t("official_website")}
          </a>
          <a href="https://app.scai.sh" target="_blank" rel="noreferrer" style={{ color: "#fff", opacity: 0.9, fontSize: "16px", fontWeight: "800" }}>
            {t("app")}
          </a>
          <a href="https://t.me/+AMy9MvWuVqhlNDY1" target="_blank" rel="noreferrer" style={{ color: "#fff", opacity: 0.9, fontSize: "16px", fontWeight: "800" }}>
            {t("community")}
          </a>
        </div>
        {/* 桌面端显示语言选择器和钱包 */}
        <motion.div initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.3, delay: 0.1 }} className="header-actions desktop-only" style={{ display: "flex", alignItems: "center", gap: "8px" }}>
          <Select
            defaultValue={i18n.language}
            style={{ height: 45, borderRadius: 2, background: "transparent", color: "#fff", border: "1px solid #fff" }}
            onChange={handleLanguageChange}
            options={[
              { value: "en", label: "English" },
              { value: "zh", label: "中文" },
            ]}
          />
          <Wallet />
        </motion.div>
      </motion.div>

      {/* 移动端显示汉堡菜单按钮 */}
      <motion.div initial={{ opacity: 0, x: 20 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.3, delay: 0.1 }} className="mobile-only" style={{ display: "none" }}>
        <Button type="text" icon={<MenuOutlined style={{ fontSize: "24px", color: "#fff" }} />} onClick={toggleDrawer} />
      </motion.div>

      {/* 抽屉式汉堡菜单 */}
      <Drawer title={t("menu")} placement="right" onClose={toggleDrawer} open={drawerVisible} bodyStyle={{ padding: 0 }} width={200}>
        {menuItems}
      </Drawer>
    </AntHeader>
  );
};
