import { Card, Typography } from "antd";

export const NewsPanel: React.FC = () => {
  return (
    <div style={{ display: "flex", flexDirection: "row", justifyContent: "space-between", alignItems: "center", width: "100%", height: "100%" }}>
      <div style={{ display: "flex", flexDirection: "row", justifyContent: "space-between", alignItems: "center", width: "100%", height: "100%" }}>
        <p>WEB3</p>
        <p>.</p>
        <p>BRAIN</p>
        <p>.</p>
        <p>OF</p>
        <p>.</p>
        <p>SCIENCE</p>
        <p>.</p>
        <p>WEB3</p>
        <p>.</p>
        <p>BRAIN</p>
        <p>.</p>
        <p>OF</p>
        <p>.</p>
        <p>SCIENCE</p>
      </div>
      <Card>
        <Typography.Title level={2}>News</Typography.Title>
      </Card>
    </div>
  );
};
