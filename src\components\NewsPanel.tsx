import { Card, Typography } from "antd";
import { LeftOutlined, RightOutlined } from "@ant-design/icons";
import { motion } from "framer-motion";
import "./NewsPanel.css";

export const NewsPanel: React.FC = () => {
  return (
    <div
      style={{
        width: "100%",
        background: "transparent",
      }}
    >
      {/* Scrolling Text Banner */}
      <div className="scrolling-banner-container">
        <div className="scrolling-banner">
          <span className="scrolling-text">WEB3 . BRAIN . OF . SCIENCE . WEB3 . BRAIN . OF . SCIENCE . WEB3 . BRAIN . OF . SCIENCE . WEB3 . BRAIN . OF . SCIENCE</span>
        </div>
      </div>

      {/* News Cards Section */}
      <div
        className="news-cards-container"
        style={{
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          gap: "20px",
          marginTop: "40px",
          flexWrap: "wrap",
        }}
      >
        {/* Left Arrow */}
        <motion.div
          className="news-arrow"
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          style={{
            cursor: "pointer",
            color: "rgba(255, 255, 255, 0.6)",
            fontSize: "24px",
            padding: "10px",
          }}
        >
          <LeftOutlined />
        </motion.div>

        {/* News Card 1 */}
        <motion.div initial={{ opacity: 0, x: -50 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.6 }}>
          <Card
            className="news-card"
            style={{
              width: "400px",
              minHeight: "200px",
              background: "linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 20%)",
              backdropFilter: "blur(30px)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              borderRadius: "16px",
              boxShadow: "0 8px 24px rgba(0, 0, 0, 0.2)",
            }}
            styles={{ body: { padding: "24px" } }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: "12px",
                marginBottom: "16px",
              }}
            >
              <div
                style={{
                  width: "8px",
                  height: "8px",
                  borderRadius: "50%",
                  background: "#ff6b35",
                }}
              />
              <span
                style={{
                  color: "rgba(255, 255, 255, 0.8)",
                  fontSize: "12px",
                  textTransform: "uppercase",
                  letterSpacing: "1px",
                }}
              >
                News
              </span>
            </div>

            <Typography.Title
              level={3}
              style={{
                color: "#ffffff",
                marginBottom: "16px",
                fontSize: "20px",
                lineHeight: "1.4",
              }}
            >
              Science saves lives...but the process is stuck in the past.
            </Typography.Title>

            <Typography.Paragraph
              style={{
                color: "rgba(255, 255, 255, 0.7)",
                fontSize: "14px",
                lineHeight: "1.6",
                marginBottom: "16px",
              }}
            >
              Before we make our major update, we want to keep our App with continuously new features, like a Decentralized Profile, which...
            </Typography.Paragraph>

            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
            >
              <span
                style={{
                  color: "rgba(255, 255, 255, 0.6)",
                  fontSize: "12px",
                }}
              >
                @ScienceDefi
              </span>
            </div>
          </Card>
        </motion.div>

        {/* News Card 2 (Duplicate for carousel effect) */}
        <motion.div initial={{ opacity: 0, x: 50 }} animate={{ opacity: 1, x: 0 }} transition={{ duration: 0.6, delay: 0.2 }}>
          <Card
            style={{
              width: "400px",
              minHeight: "200px",
              background: "linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 20%)",
              backdropFilter: "blur(30px)",
              border: "1px solid rgba(255, 255, 255, 0.2)",
              borderRadius: "16px",
              boxShadow: "0 8px 24px rgba(0, 0, 0, 0.2)",
            }}
            styles={{ body: { padding: "24px" } }}
          >
            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: "12px",
                marginBottom: "16px",
              }}
            >
              <div
                style={{
                  width: "8px",
                  height: "8px",
                  borderRadius: "50%",
                  background: "#ff6b35",
                }}
              />
              <span
                style={{
                  color: "rgba(255, 255, 255, 0.8)",
                  fontSize: "12px",
                  textTransform: "uppercase",
                  letterSpacing: "1px",
                }}
              >
                News
              </span>
            </div>

            <Typography.Title
              level={3}
              style={{
                color: "#ffffff",
                marginBottom: "16px",
                fontSize: "20px",
                lineHeight: "1.4",
              }}
            >
              Science saves lives...but the process is stuck in the past.
            </Typography.Title>

            <Typography.Paragraph
              style={{
                color: "rgba(255, 255, 255, 0.7)",
                fontSize: "14px",
                lineHeight: "1.6",
                marginBottom: "16px",
              }}
            >
              Before we make our major update, we want to keep our App with continuously new features, like a Decentralized Profile, which...
            </Typography.Paragraph>

            <div
              style={{
                display: "flex",
                alignItems: "center",
                gap: "8px",
              }}
            >
              <span
                style={{
                  color: "rgba(255, 255, 255, 0.6)",
                  fontSize: "12px",
                }}
              >
                @ScienceDefi
              </span>
            </div>
          </Card>
        </motion.div>

        {/* Right Arrow */}
        <motion.div
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          style={{
            cursor: "pointer",
            color: "rgba(255, 255, 255, 0.6)",
            fontSize: "24px",
            padding: "10px",
          }}
        >
          <RightOutlined />
        </motion.div>
      </div>
    </div>
  );
};
