// 调试缓存问题的脚本
// 在浏览器控制台中运行

console.log('🔧 开始调试缓存问题...');

// 1. 检查当前缓存
function debugCache() {
    const cached = localStorage.getItem('irys_stats_cache');
    console.log('📊 原始缓存数据:', cached);
    
    if (cached) {
        try {
            const parsed = JSON.parse(cached);
            console.log('📊 解析后的缓存:', parsed);
            console.log('📊 totalCount 类型:', typeof parsed.totalCount);
            console.log('📊 totalCount 值:', parsed.totalCount);
        } catch (e) {
            console.log('❌ 缓存解析失败:', e);
        }
    }
}

// 2. 清除缓存
function clearCache() {
    localStorage.removeItem('irys_stats_cache');
    console.log('✅ 缓存已清除');
}

// 3. 强制重新获取
function forceRefresh() {
    clearCache();
    window.location.reload();
}

// 运行调试
console.log('\n=== 当前缓存状态 ===');
debugCache();

console.log('\n=== 可用命令 ===');
console.log('debugCache() - 检查缓存详情');
console.log('clearCache() - 清除缓存');
console.log('forceRefresh() - 清除缓存并刷新页面');

// 暴露函数到全局
window.debugCache = debugCache;
window.clearCache = clearCache;
window.forceRefresh = forceRefresh;

// 检查是否有问题的缓存
const cached = localStorage.getItem('irys_stats_cache');
if (cached) {
    try {
        const parsed = JSON.parse(cached);
        if (typeof parsed.totalCount !== 'number') {
            console.log('\n⚠️  检测到 totalCount 类型异常!');
            console.log('💡 建议运行 forceRefresh() 清除并重新获取');
        }
    } catch (e) {
        console.log('\n❌ 缓存数据损坏!');
        console.log('💡 建议运行 forceRefresh() 清除并重新获取');
    }
}
