// 极简化统计 - 只统计Version 2.0.0 PDF
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function getSimpleStats() {
    console.log('📊 获取Version 2.0.0 PDF统计（分页查询）...');
    
    let allDois = new Set();
    let cursor = null;
    let pageCount = 0;
    let totalFiles = 0;
    
    while (pageCount < 50) { // 安全限制
        pageCount++;
        console.log(`📖 查询第 ${pageCount} 页...`);
        
        const query = `
            query {
                transactions(
                    tags: [
                        { name: "App-Name", values: ["scivault"] },
                        { name: "Content-Type", values: ["application/pdf"] },
                        { name: "Version", values: ["2.0.0"] }
                    ],
                    first: 1000,
                    order: DESC
                    ${cursor ? `after: "${cursor}"` : ''}
                ) {
                    edges {
                        node {
                            id
                            tags {
                                name
                                value
                            }
                        }
                        cursor
                    }
                }
            }
        `;

        try {
            const response = await fetch('https://uploader.irys.xyz/graphql', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ query })
            });

            const result = await response.json();
            const edges = result.data?.transactions?.edges || [];
            
            if (edges.length === 0) {
                console.log(`✅ 第 ${pageCount} 页没有数据，查询结束`);
                break;
            }
            
            // 提取DOI
            edges.forEach(edge => {
                const doi = edge.node.tags.find(tag => tag.name === 'doi')?.value;
                if (doi) {
                    allDois.add(doi);
                }
            });
            
            totalFiles += edges.length;
            console.log(`📄 第 ${pageCount} 页: ${edges.length} 个文件, 累计唯一DOI: ${allDois.size} 个`);
            
            if (edges.length < 100) {
                console.log(`✅ 第 ${pageCount} 页数据不足100条，已到最后一页`);
                break;
            }
            
            cursor = edges[edges.length - 1]?.cursor;
            if (!cursor) {
                console.log(`✅ 第 ${pageCount} 页没有cursor，已到最后一页`);
                break;
            }
            
        } catch (error) {
            console.error(`❌ 第 ${pageCount} 页查询出错:`, error.message);
            break;
        }
    }

    console.log(`\n🎉 查询完成！`);
    console.log(`📁 Version 2.0.0 PDF总数: ${allDois.size} 篇`);
    console.log(`📄 PDF文件总数: ${totalFiles} 个`);
    
    return allDois.size;
}

// 运行统计
if (require.main === module) {
    getSimpleStats().catch(console.error);
} 