import { useEffect, useState } from 'react';
import { But<PERSON>, Card, List, message, Row, Col, Statistic } from 'antd';
import { Program, BN } from '@coral-xyz/anchor';
import { PublicKey, SystemProgram } from '@solana/web3.js';
import { useConnection, useWallet } from '@solana/wallet-adapter-react';
import { TOKEN_PROGRAM_ID, ASSOCIATED_TOKEN_PROGRAM_ID, getAssociatedTokenAddressSync } from '@solana/spl-token';
import { ScihubLock } from '../types/scihub_lock';
import { motion } from 'framer-motion';
import { Buffer as BufferPolyfill } from 'buffer';
import { WalletOutlined, LockOutlined, GiftOutlined, ClockCircleOutlined } from '@ant-design/icons';
import idl from '../types/scihub.json';

// Polyfill Buffer for browser environments
globalThis.Buffer = BufferPolyfill;

const PROGRAM_ID = new PublicKey('HqmtqTCGNjN7KvcMkKPEzVqLYwReXCSL2QhFvUsdse33');
const TOKEN_MINT = new PublicKey('A22hchYQ2Eiwe7k57ALGmDwN4oJYzn11oadKiuALaNZs');

export const AccountOverview: React.FC = () => {
    const { connection } = useConnection();
    const { publicKey, sendTransaction } = useWallet();
    const [userLockInfo, setUserLockInfo] = useState<any>(null);
    const [userLocks, setUserLocks] = useState<any[]>([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
        if (!publicKey) return;
        const fetchUserData = async () => {
            try {
                const program = new Program<ScihubLock>(idl as any, { connection });
                const [projectLock] = PublicKey.findProgramAddressSync(
                    [Buffer.from('project_lock'), TOKEN_MINT.toBuffer()],
                    PROGRAM_ID
                );
                const [userLockInfoAddr] = PublicKey.findProgramAddressSync(
                    [Buffer.from('user_lock_info'), publicKey.toBuffer(), projectLock.toBuffer()],
                    PROGRAM_ID
                );

                let userLockInfoData = null;
                try {
                    userLockInfoData = await program.account.userLockInfo.fetch(userLockInfoAddr);
                    setUserLockInfo(userLockInfoData);
                } catch (fetchError) {
                    console.warn(`Could not fetch UserLockInfo at ${userLockInfoAddr.toBase58()}:`, fetchError);
                    message.error('Failed to fetch user lock info');
                }

                const accounts = await connection.getProgramAccounts(PROGRAM_ID, {
                    filters: [{ memcmp: { offset: 16, bytes: publicKey.toBase58() } }],
                });
                const userLockData = await program.account.userLock.fetchMultiple(accounts.map(acc => acc.pubkey));
                setUserLocks(userLockData.filter(Boolean));
            } catch (error) {
                message.error('Failed to fetch user data');
                console.error('Error fetching user data:', error);
            }
        };
        fetchUserData();
    }, [publicKey, connection]);

    const onClaimReward = async () => {
        if (!publicKey) {
            message.error('Please connect your wallet');
            return;
        }
        setLoading(true);
        try {
            const program = new Program<ScihubLock>(idl as any, { connection });
            const [projectLock] = PublicKey.findProgramAddressSync(
                [Buffer.from('project_lock'), TOKEN_MINT.toBuffer()],
                PROGRAM_ID
            );
            const [userLockInfo] = PublicKey.findProgramAddressSync(
                [Buffer.from('user_lock_info'), publicKey.toBuffer(), projectLock.toBuffer()],
                PROGRAM_ID
            );
            const userRewardTokenAccount = getAssociatedTokenAddressSync(TOKEN_MINT, publicKey);
            const projectRewardTokenAccount = getAssociatedTokenAddressSync(TOKEN_MINT, projectLock, true);

            const tx = await program.methods
                .claimReward()
                .accounts({
                    user: publicKey,
                    tokenMint: TOKEN_MINT,
                    rewardTokenMint: TOKEN_MINT,
                    userRewardTokenAccount,
                    projectRewardTokenAccount,
                    projectLock,
                    userLockInfo,
                    tokenProgram: TOKEN_PROGRAM_ID,
                    associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
                    systemProgram: SystemProgram.programId,
                })
                .transaction();

            const txId = await sendTransaction(tx, connection, { skipPreflight: true });
            await connection.confirmTransaction(txId);
            message.success(`Reward claimed successfully! Tx: ${txId}`);
        } catch (error) {
            message.error(`Claim failed: ${(error as Error).message}`);
            console.error('Claim error:', error);
        } finally {
            setLoading(false);
        }
    };

    const onUnlock = async (index: number) => {
        if (!publicKey) {
            message.error('Please connect your wallet');
            return;
        }
        setLoading(true);
        try {
            const program = new Program<ScihubLock>(idl as any, { connection });
            const [projectLock] = PublicKey.findProgramAddressSync(
                [Buffer.from('project_lock'), TOKEN_MINT.toBuffer()],
                PROGRAM_ID
            );
            const [userLockInfo] = PublicKey.findProgramAddressSync(
                [Buffer.from('user_lock_info'), publicKey.toBuffer(), projectLock.toBuffer()],
                PROGRAM_ID
            );
            const userTokenAccount = getAssociatedTokenAddressSync(TOKEN_MINT, publicKey);
            const lockTokenAccount = getAssociatedTokenAddressSync(TOKEN_MINT, projectLock, true);
            const [userLock] = PublicKey.findProgramAddressSync(
                [Buffer.from('user_lock'), publicKey.toBuffer(), TOKEN_MINT.toBuffer(), new BN(index).toArrayLike(Buffer, 'le', 8)],
                PROGRAM_ID
            );

            const tx = await program.methods
                .unlock(new BN(index))
                .accounts({
                    user: publicKey,
                    tokenMint: TOKEN_MINT,
                    userTokenAccount,
                    lockTokenAccount,
                    projectLock,
                    userLockInfo,
                    userLock,
                    tokenProgram: TOKEN_PROGRAM_ID,
                    associatedTokenProgram: ASSOCIATED_TOKEN_PROGRAM_ID,
                    systemProgram: SystemProgram.programId,
                })
                .transaction();

            const txId = await sendTransaction(tx, connection, { skipPreflight: true });
            await connection.confirmTransaction(txId);
            message.success(`Unlocked successfully! Tx: ${txId}`);
        } catch (error) {
            message.error(`Unlock failed: ${(error as Error).message}`);
            console.error('Unlock error:', error);
        } finally {
            setLoading(false);
        }
    };

    // Helper function to safely convert BN to number
    const toNumber = (value: any): number => {
        if (BN.isBN(value)) {
            return value.toNumber();
        }
        return Number(value) || 0; // Fallback to 0 if conversion fails
    };

    return (
        <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            style={{ maxWidth: 800, margin: '0 auto' }}
        >
            <Card
                title={<span style={{ color: '#fff', fontSize: '24px', fontWeight: 'bold' }}>Your Account</span>}
                style={{
                    marginBottom: 24,
                    background: 'linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%)',
                    borderRadius: 8,
                    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
                    border: 'none',
                }}
                headStyle={{ background: 'transparent', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}
            >
                {userLockInfo ? (
                    <>
                        <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
                            <Col span={8}>
                                <Statistic
                                    title={<span style={{ color: '#fff' }}>Total Staked</span>}
                                    value={(toNumber(userLockInfo.amount) / 1_000_000_000).toFixed(2)}
                                    prefix={<WalletOutlined style={{ color: '#fff' }} />}
                                    valueStyle={{ color: '#fff', fontSize: '1.5em' }}
                                    suffix="Tokens"
                                />
                            </Col>
                            <Col span={8}>
                                <Statistic
                                    title={<span style={{ color: '#fff' }}>Accumulated Rewards</span>}
                                    value={(toNumber(userLockInfo.accumulatedReward) / 1_000_000_000).toFixed(2)}
                                    prefix={<GiftOutlined style={{ color: '#fff' }} />}
                                    valueStyle={{ color: '#fff', fontSize: '1.5em' }}
                                    suffix="Tokens"
                                />
                            </Col>
                            <Col span={8}>
                                <Statistic
                                    title={<span style={{ color: '#fff' }}>Received Rewards</span>}
                                    value={(toNumber(userLockInfo.receivedReward) / 1_000_000_000).toFixed(2)}
                                    prefix={<GiftOutlined style={{ color: '#fff' }} />}
                                    valueStyle={{ color: '#fff', fontSize: '1.5em' }}
                                    suffix="Tokens"
                                />
                            </Col>
                        </Row>
                        <Button
                            type="primary"
                            size="large"
                            onClick={onClaimReward}
                            loading={loading}
                            style={{
                                marginBottom: 24,
                                background: 'linear-gradient(45deg, #1890ff, #40c4ff)',
                                border: 'none',
                                borderRadius: 4,
                                transition: 'all 0.3s',
                            }}
                            onMouseEnter={(e) => (e.currentTarget.style.transform = 'scale(1.05)')}
                            onMouseLeave={(e) => (e.currentTarget.style.transform = 'scale(1)')}
                        >
                            Claim Rewards
                        </Button>
                        <List
                            header={<div style={{ color: '#fff', fontSize: '1.2em', fontWeight: 'bold' }}>Your Locks</div>}
                            dataSource={userLocks}
                            renderItem={(lock: any) => (
                                <List.Item
                                    style={{
                                        background: 'rgba(255, 255, 255, 0.05)',
                                        borderRadius: 4,
                                        marginBottom: 8,
                                        padding: 16,
                                        transition: 'all 0.3s',
                                        cursor: 'pointer',
                                    }}
                                    onMouseEnter={(e) => (e.currentTarget.style.background = 'rgba(255, 255, 255, 0.1)')}
                                    onMouseLeave={(e) => (e.currentTarget.style.background = 'rgba(255, 255, 255, 0.05)')}
                                    actions={[
                                        <Button
                                            key="unlock"
                                            type="primary"
                                            onClick={() => onUnlock(toNumber(lock.index))}
                                            disabled={toNumber(lock.endTime) > Math.floor(Date.now() / 1000)}
                                            style={{
                                                background: toNumber(lock.endTime) > Math.floor(Date.now() / 1000)
                                                    ? '#595959'
                                                    : 'linear-gradient(45deg, #ff4d4f, #ff7875)',
                                                border: 'none',
                                                borderRadius: 4,
                                                transition: 'all 0.3s',
                                            }}
                                            onMouseEnter={(e) => (e.currentTarget.style.transform = 'scale(1.05)')}
                                            onMouseLeave={(e) => (e.currentTarget.style.transform = 'scale(1)')}
                                        >
                                            Unlock
                                        </Button>,
                                    ]}
                                >
                                    <List.Item.Meta
                                        avatar={<LockOutlined style={{ color: '#fff', fontSize: '1.5em' }} />}
                                        title={<span style={{ color: '#fff' }}>Lock #{toNumber(lock.index)}</span>}
                                        description={
                                            <span style={{ color: 'rgba(255, 255, 255, 0.85)' }}>
                                                Amount: {(toNumber(lock.amount) / 1_000_000_000).toFixed(2)} Tokens
                                                <br />
                                                Ends: <ClockCircleOutlined style={{ marginLeft: 8, marginRight: 4 }} />
                                                {new Date(toNumber(lock.endTime) * 1000).toLocaleString()}
                                            </span>
                                        }
                                    />
                                </List.Item>
                            )}
                        />
                    </>
                ) : (
                    <div style={{ color: '#fff', textAlign: 'center', padding: '24px 0' }}>
                        <WalletOutlined style={{ fontSize: '2em', marginBottom: 8 }} />
                        <p>Connect your wallet to view account details.</p>
                    </div>
                )}
            </Card>
        </motion.div>
    );
};