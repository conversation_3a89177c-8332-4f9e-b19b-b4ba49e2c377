import { Layout, Typography } from 'antd';
import { motion } from 'framer-motion';
import { GithubOutlined } from '@ant-design/icons';
// Note: Ant Design doesn't have X or Telegram icons, so we'll use text or custom icons
import { SiX, SiTelegram } from 'react-icons/si'; // Using react-icons for X and Telegram

const { Footer: AntFooter } = Layout;
const { Text } = Typography;

export const Footer: React.FC = () => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.8 }}
      style={{ marginTop: '24px' }}
    >
      <AntFooter
        style={{
          textAlign: 'center',
          background: 'linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%)',
          padding: '24px 50px',
          boxShadow: '0 -2px 4px rgba(0, 0, 0, 0.2)',
          borderTop: '1px solid rgba(255, 255, 255, 0.1)',
        }}
      >
        <Text
          style={{
            color: '#fff',
            fontSize: '16px',
            fontWeight: '500',
          }}
        >
          SCAI Community Foundation © 2025 | Powered by Solana
        </Text>
        <div style={{ marginTop: 12 }}>
          <motion.a
            href="https://x.com/SCAI_Agents"
            target="_blank"
            rel="noopener noreferrer"
            style={{
              margin: '0 16px',
              color: '#40c4ff',
              fontSize: '16px',
              display: 'inline-flex',
              alignItems: 'center',
              transition: 'all 0.3s',
            }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <SiX style={{ marginRight: 8, fontSize: 24 }} />
          </motion.a>
          <motion.a
            href="t.me/WTFDeSci"
            target="_blank"
            rel="noopener noreferrer"
            style={{
              margin: '0 16px',
              color: '#40c4ff',
              fontSize: '16px',
              display: 'inline-flex',
              alignItems: 'center',
              transition: 'all 0.3s',
            }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <SiTelegram style={{ marginRight: 8, fontSize: 24 }} />
          </motion.a>
          <motion.a
            href="https://github.com/SCAI-Foundation"
            target="_blank"
            rel="noopener noreferrer"
            style={{
              margin: '0 16px',
              color: '#40c4ff',
              fontSize: '16px',
              display: 'inline-flex',
              alignItems: 'center',
              transition: 'all 0.3s',
            }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.95 }}
          >
            <GithubOutlined style={{ marginRight: 8, fontSize: 24 }} />
          </motion.a>
        </div>
      </AntFooter>
    </motion.div>
  );
};