{
  "compilerOptions": {
    // 现有配置...
    "strict": false, // 禁用严格模式
    "noImplicitAny": false, // 允许隐式 any
    "skipLibCheck": true, // 跳过库类型检查
    "ignoreDeprecations": "5.0", // 忽略废弃警告
    // 如果需要更激进
    "noEmitOnError": false, // 即使有错误也生成输出
    "target": "ESNext",
    "useDefineForClassFields": true,
    "lib": [
      "DOM",
      "DOM.Iterable",
      "ESNext"
    ],
    "allowJs": false,
    "esModuleInterop": true,
    "allowSyntheticDefaultImports": true,
    "forceConsistentCasingInFileNames": true,
    "module": "ESNext",
    "moduleResolution": "Node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "noEmit": true,
    "jsx": "react-jsx",
    "baseUrl": "src",
    "paths": {
      "@/*": [
        "./*"
      ]
    }
  },
  "include": [
    "src"
  ]
}