import { FC } from "react";
import { WalletModalProvider, WalletMultiButton } from "@solana/wallet-adapter-react-ui";
import "@solana/wallet-adapter-react-ui/styles.css";

export const Wallet: FC = () => {
  return (
    <div style={{ display: "flex", alignContent: "center" }}>
      <WalletModalProvider>
        <WalletMultiButton style={{ padding: "0 16px", backgroundColor: "rgba(255, 51, 20, 1)", color: "#fff", borderRadius: 2 }} />
      </WalletModalProvider>
    </div>
  );
};
