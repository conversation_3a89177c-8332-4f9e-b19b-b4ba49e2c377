import { useEffect, useState } from 'react';
import { Card, Col, Row, Statistic } from 'antd';
import { Program } from '@coral-xyz/anchor';
import { PublicKey, Connection } from '@solana/web3.js';
import { useConnection } from '@solana/wallet-adapter-react';
import { ScihubLock } from '../types/scihub_lock';
import { motion } from 'framer-motion';
import { getAssociatedTokenAddressSync, getAccount } from '@solana/spl-token';
import idl from '../types/scihub.json';
import { Buffer as BufferPolyfill } from 'buffer';
import { WalletOutlined, FileTextOutlined, FundOutlined, ApiOutlined } from '@ant-design/icons';

// Polyfill Buffer for browser environments
globalThis.Buffer = BufferPolyfill;

// Program and token constants
const PROGRAM_ID = new PublicKey('HqmtqTCGNjN7KvcMkKPEzVqLYwReXCSL2QhFvUsdse33');
const TOKEN_MINT = new PublicKey('A22hchYQ2Eiwe7k57ALGmDwN4oJYzn11oadKiuALaNZs');
const FOUNDATION_1 = new PublicKey('Cfy5rFwmU2fe43YbR79F1Nsm1REZ9DurQ3aoKhP6ANgt');
const FOUNDATION_2 = new PublicKey('di8tq1XvpWp232d9Keu2j7NHxi9t8SaAqDpybeWPKa8');

// Define types for Irys GraphQL response
interface Tag {
    name: string;
    value: string;
}

interface Node {
    id: string;
    tags: Tag[];
}

interface Edge {
    node: Node;
    cursor: string;
}

interface Transactions {
    edges: Edge[];
}

interface GraphQLResponse {
    data?: {
        transactions?: Transactions;
    };
}

export const StatsPanel: React.FC = () => {
    const { connection } = useConnection();
    const [stats, setStats] = useState({
        totalAmount: 0,
        rewardPerSec: 0,
        foundation1Balance: 0,
        foundation2Balance: 0,
        researchPapersUploaded: 0,
        scaiEngineStatus: 'Offline' as 'Online' | 'Offline',
    });
    const [initialLoading, setInitialLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Fetch Irys PDF statistics
    const getIrysStats = async (): Promise<number> => {
        let allDois = new Set<string>();
        let cursor: string | null = null;
        let pageCount = 0;

        while (pageCount < 10000) {
            pageCount++;
            const query = `
        query {
          transactions(
            tags: [
              { name: "App-Name", values: ["scivault"] },
              { name: "Content-Type", values: ["application/pdf"] },
              { name: "Version", values: ["2.0.0"] }
            ],
            first: 1000,
            order: DESC
            ${cursor ? `after: "${cursor}"` : ''}
          ) {
            edges {
              node {
                id
                tags {
                  name
                  value
                }
              }
              cursor
            }
          }
        }
      `;

            try {
                const response = await fetch('https://uploader.irys.xyz/graphql', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ query }),
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }

                const result: GraphQLResponse = await response.json();
                const edges = result.data?.transactions?.edges || [];

                if (edges.length === 0) {
                    break;
                }

                edges.forEach(edge => {
                    const doi = edge.node.tags.find(tag => tag.name === 'doi')?.value;
                    if (doi) {
                        allDois.add(doi);
                    }
                });

                if (edges.length < 100) {
                    break;
                }

                cursor = edges[edges.length - 1]?.cursor;
                if (!cursor) {
                    break;
                }
            } catch (err) {
                console.warn('Irys fetch error:', err);
                throw err;
            }
        }

        return allDois.size;
    };

    // Check SCAI Engine status
    const checkScaiStatus = async (): Promise<'Online' | 'Offline'> => {
        try {
            const response = await fetch('https://api.scai.sh/dois?page=1', {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' },
            });

            return response.ok ? 'Online' : 'Offline';
        } catch (err) {
            console.warn('SCAI status check error:', err);
            return 'Offline';
        }
    };

    // Fetch all stats (Solana, Irys, SCAI)
    const fetchStats = async (isInitialFetch: boolean) => {
        if (isInitialFetch) {
            setInitialLoading(true);
        }
        setError(null);
        try {
            if (!connection) {
                setError('Connection not ready.');
                if (isInitialFetch) {
                    setInitialLoading(false);
                }
                return;
            }

            // Derive project lock PDA
            const [projectLockPDA] = PublicKey.findProgramAddressSync(
                [Buffer.from('project_lock'), TOKEN_MINT.toBuffer()],
                PROGRAM_ID
            );

            // Initialize Anchor program
            const program = new Program<ScihubLock>(idl as any, { connection });

            // Fetch project lock account
            let projectLockAccount = null;
            try {
                projectLockAccount = await program.account.projectLock.fetch(projectLockPDA);
                console.log('Project Lock Account Data:', projectLockAccount);
            } catch (fetchError: any) {
                console.warn(`Could not fetch ProjectLock account at ${projectLockPDA.toBase58()}:`, fetchError.message);
                setError(`ProjectLock account not found or uninitialized: ${fetchError.message}`);
                if (isInitialFetch) {
                    setInitialLoading(false);
                }
                return;
            }

            // Derive and fetch lock token account (ATA for projectLockPDA)
            const lockTokenAccount = getAssociatedTokenAddressSync(
                TOKEN_MINT,
                projectLockPDA,
                true
            );

            let lockTokenBalance = 0;
            try {
                const lockTokenAccountInfo = await getAccount(connection, lockTokenAccount);
                lockTokenBalance = Number(lockTokenAccountInfo.amount) / 1_000_000_000;
            } catch (error: any) {
                console.warn(`Lock token account not found at ${lockTokenAccount.toBase58()}:`, error.message);
            }

            // Fetch foundation wallet token balances
            const foundation1ATA = getAssociatedTokenAddressSync(TOKEN_MINT, FOUNDATION_1);
            const foundation2ATA = getAssociatedTokenAddressSync(TOKEN_MINT, FOUNDATION_2);

            let foundation1Balance = 0;
            let foundation2Balance = 0;

            try {
                const foundation1Account = await getAccount(connection, foundation1ATA);
                foundation1Balance = Number(foundation1Account.amount) / 1_000_000_000;
            } catch (error: any) {
                console.warn(`Foundation 1 token account not found at ${foundation1ATA.toBase58()}:`, error.message);
            }

            try {
                const foundation2Account = await getAccount(connection, foundation2ATA);
                foundation2Balance = Number(foundation2Account.amount) / 1_000_000_000;
            } catch (error: any) {
                console.warn(`Foundation 2 token account not found at ${foundation2ATA.toBase58()}:`, error.message);
            }

            // Fetch Irys and SCAI stats
            let researchPapersUploaded = 0;
            let scaiEngineStatus: 'Online' | 'Offline' = 'Offline';
            try {
                researchPapersUploaded = await getIrysStats();
            } catch (err) {
                console.warn('Failed to fetch Irys stats:', err);
            }

            try {
                scaiEngineStatus = await checkScaiStatus();
            } catch (err) {
                console.warn('Failed to fetch SCAI status:', err);
            }

            // Update stats
            setStats({
                totalAmount: projectLockAccount
                    ? Number(projectLockAccount.totalAmount) / 1_000_000_000
                    : 0,
                rewardPerSec: projectLockAccount
                    ? Number(projectLockAccount.rewardTokenPerSec) / 1_000_000_000
                    : 0,
                foundation1Balance,
                foundation2Balance,
                researchPapersUploaded,
                scaiEngineStatus,
            });
        } catch (err: any) {
            console.error('Error fetching stats:', err);
            setError('Failed to fetch stats: ' + err.message);
        } finally {
            if (isInitialFetch) {
                setInitialLoading(false);
            }
        }
    };

    useEffect(() => {
        // Initial fetch
        fetchStats(true);

        // Polling every 15 seconds (silent)
        const interval = setInterval(() => fetchStats(false), 15000);
        return () => clearInterval(interval);
    }, [connection]);

    return (
        <motion.div
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            style={{ width: '800px', margin: '0 auto' }} // Changed from maxWidth to width
        >
            <Card
                title={
                    <span style={{ color: '#fff', fontSize: '24px', fontWeight: 'bold' }}>
                        <FundOutlined style={{ marginRight: 8, color: '#40c4ff' }} />
                        Foundation Stats
                    </span>
                }
                style={{
                    marginBottom: 24,
                    background: 'linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%)',
                    borderRadius: 8,
                    boxShadow: '0 4px 8px rgba(0, 0, 0, 0.2)',
                    border: 'none',
                }}
                headStyle={{ background: 'transparent', borderBottom: '1px solid rgba(255, 255, 255, 0.1)' }}
                loading={initialLoading}
            >
                {error && (
                    <div style={{ color: 'rgba(255, 75, 75, 0.85)', marginBottom: 16, textAlign: 'center' }}>
                        Error: {error}
                    </div>
                )}
                <Row gutter={[24, 16]}>
                    <Col span={12}>
                        <Statistic
                            title={<span style={{ color: '#fff' }}>Total Staked</span>}
                            value={stats.totalAmount.toFixed(2)}
                            prefix={<WalletOutlined style={{ color: '#40c4ff' }} />}
                            valueStyle={{ color: '#fff', fontSize: '1.5em' }}
                            suffix="Tokens"
                        />
                    </Col>
                    <Col span={12}>
                        <Statistic
                            title={<span style={{ color: '#fff' }}>Reward Rate</span>}
                            value={stats.rewardPerSec.toFixed(6)}
                            prefix={<FundOutlined style={{ color: '#40c4ff' }} />}
                            valueStyle={{ color: '#fff', fontSize: '1.5em' }}
                            suffix="Tokens/Sec"
                        />
                    </Col>
                    <Col span={12}>
                        <Statistic
                            title={<span style={{ color: '#fff' }}>Foundation 1 Balance</span>}
                            value={stats.foundation1Balance.toFixed(2)}
                            prefix={<WalletOutlined style={{ color: '#40c4ff' }} />}
                            valueStyle={{ color: '#fff', fontSize: '1.5em' }}
                            suffix="Tokens"
                        />
                    </Col>
                    <Col span={12}>
                        <Statistic
                            title={<span style={{ color: '#fff' }}>Foundation 2 Balance</span>}
                            value={stats.foundation2Balance.toFixed(2)}
                            prefix={<WalletOutlined style={{ color: '#40c4ff' }} />}
                            valueStyle={{ color: '#fff', fontSize: '1.5em' }}
                            suffix="Tokens"
                        />
                    </Col>
                    <Col span={12}>
                        <Statistic
                            title={<span style={{ color: '#fff' }}>Research Papers Uploaded</span>}
                            value={stats.researchPapersUploaded}
                            prefix={<FileTextOutlined style={{ color: '#40c4ff' }} />}
                            valueStyle={{ color: '#fff', fontSize: '1.5em' }}
                            suffix="Papers"
                        />
                    </Col>
                    <Col span={12}>
                        <Statistic
                            title={<span style={{ color: '#fff' }}>SCAI Engine Status</span>}
                            value={stats.scaiEngineStatus}
                            prefix={<ApiOutlined style={{ color: '#40c4ff' }} />}
                            valueStyle={{
                                color: stats.scaiEngineStatus === 'Online' ? '#40c4ff' : 'rgba(255, 75, 75, 0.85)',
                                fontSize: '1.5em',
                            }}
                        />
                    </Col>
                </Row>
            </Card>
        </motion.div>
    );
};