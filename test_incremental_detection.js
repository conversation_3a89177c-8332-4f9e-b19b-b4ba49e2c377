// 测试增量检测功能
// 在浏览器控制台中运行

console.log('🔍 测试增量检测功能...');

// 1. 检查当前缓存状态
function checkCacheStatus() {
    const cached = localStorage.getItem('irys_stats_cache');
    if (cached) {
        try {
            const data = JSON.parse(cached);
            console.log('📊 当前缓存状态:');
            console.log(`   - 总数: ${data.totalCount}`);
            console.log(`   - 最后更新: ${new Date(data.lastUpdate).toLocaleString()}`);
            console.log(`   - 游标: ${data.lastCursor ? data.lastCursor.substring(0, 30) + '...' : 'null'}`);
            
            const age = Date.now() - data.lastUpdate;
            const ageMinutes = Math.floor(age / (1000 * 60));
            console.log(`   - 缓存年龄: ${ageMinutes} 分钟`);
            
            if (ageMinutes < 5) {
                console.log('✅ 缓存仍然有效（< 5分钟）');
                console.log('💡 下次访问会检查新数据');
            } else {
                console.log('⏰ 缓存已过期（≥ 5分钟）');
                console.log('💡 下次访问会直接刷新');
            }
            
            return data;
        } catch (e) {
            console.log('❌ 缓存解析失败:', e);
            return null;
        }
    } else {
        console.log('📊 无缓存数据');
        return null;
    }
}

// 2. 模拟新数据检测
async function simulateNewDataCheck() {
    const cached = localStorage.getItem('irys_stats_cache');
    if (!cached) {
        console.log('❌ 无缓存数据，无法模拟检测');
        return;
    }
    
    try {
        const data = JSON.parse(cached);
        const lastCursor = data.lastCursor;
        
        console.log('🔍 模拟新数据检测...');
        console.log(`🔍 使用缓存游标: ${lastCursor ? lastCursor.substring(0, 30) + '...' : 'null'}`);
        
        // 获取最新数据
        const query = `
            query {
                transactions(
                    tags: [
                        { name: "App-Name", values: ["scivault"] },
                        { name: "Content-Type", values: ["application/pdf"] },
                        { name: "Version", values: ["2.0.0"] }
                    ],
                    first: 100,
                    order: DESC
                ) {
                    edges {
                        cursor
                    }
                }
            }
        `;
        
        const response = await fetch('https://uploader.irys.xyz/graphql', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ query }),
        });
        
        const result = await response.json();
        const edges = result.data?.transactions?.edges || [];
        
        if (edges.length === 0) {
            console.log('❌ 无法获取最新数据');
            return;
        }
        
        const latestCursor = edges[0]?.cursor;
        console.log(`🔍 最新游标: ${latestCursor ? latestCursor.substring(0, 30) + '...' : 'null'}`);
        
        const hasNewData = latestCursor !== lastCursor;
        console.log(`🔍 检测结果: ${hasNewData ? '有新数据 🆕' : '无新数据 ✅'}`);
        
        if (hasNewData) {
            console.log('💡 建议刷新页面获取最新数据');
        } else {
            console.log('💡 当前缓存数据是最新的');
        }
        
        return hasNewData;
        
    } catch (error) {
        console.log('❌ 检测失败:', error);
        return null;
    }
}

// 3. 测试缓存刷新
function testCacheRefresh() {
    console.log('🔄 测试缓存刷新...');
    
    // 检查当前状态
    const currentCache = checkCacheStatus();
    
    if (currentCache) {
        const age = Date.now() - currentCache.lastUpdate;
        const ageMinutes = Math.floor(age / (1000 * 60));
        
        if (ageMinutes < 5) {
            console.log('⚠️  缓存仍然有效，刷新页面会触发新数据检测');
        } else {
            console.log('⏰ 缓存已过期，刷新页面会直接重新扫描');
        }
    }
    
    console.log('🔄 3秒后刷新页面...');
    setTimeout(() => {
        window.location.reload();
    }, 3000);
}

// 4. 强制触发新数据检测
function forceNewDataDetection() {
    console.log('🔧 强制触发新数据检测...');
    
    // 修改缓存时间戳，使其看起来是最近的
    const cached = localStorage.getItem('irys_stats_cache');
    if (cached) {
        try {
            const data = JSON.parse(cached);
            data.lastUpdate = Date.now() - (2 * 60 * 1000); // 2分钟前
            localStorage.setItem('irys_stats_cache', JSON.stringify(data));
            console.log('✅ 已修改缓存时间戳为2分钟前');
            console.log('💡 现在刷新页面会触发新数据检测');
        } catch (e) {
            console.log('❌ 修改缓存失败:', e);
        }
    } else {
        console.log('❌ 无缓存数据可修改');
    }
}

// 暴露函数到全局
window.checkCacheStatus = checkCacheStatus;
window.simulateNewDataCheck = simulateNewDataCheck;
window.testCacheRefresh = testCacheRefresh;
window.forceNewDataDetection = forceNewDataDetection;

// 自动运行初始检查
console.log('\n=== 当前状态 ===');
checkCacheStatus();

console.log('\n=== 可用命令 ===');
console.log('checkCacheStatus() - 检查缓存状态');
console.log('simulateNewDataCheck() - 模拟新数据检测');
console.log('testCacheRefresh() - 测试缓存刷新');
console.log('forceNewDataDetection() - 强制触发新数据检测');

console.log('\n=== 测试建议 ===');
console.log('1. 运行 simulateNewDataCheck() 检查当前是否有新数据');
console.log('2. 运行 forceNewDataDetection() 然后刷新页面测试检测功能');
console.log('3. 观察控制台日志中的检测过程');

console.log('\n🔍 增量检测测试准备完成！');
