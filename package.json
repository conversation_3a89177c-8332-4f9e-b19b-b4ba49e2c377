{"name": "scihub-foundation", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@coral-xyz/anchor": "^0.31.1", "@emotion/css": "^11.13.5", "@esbuild-plugins/node-globals-polyfill": "^0.2.3", "@esbuild-plugins/node-modules-polyfill": "^0.2.2", "@solana/spl-memo": "^0.2.5", "@solana/spl-token": "^0.4.8", "@solana/wallet-adapter-base": "^0.9.27", "@solana/wallet-adapter-react": "^0.15.39", "@solana/wallet-adapter-react-ui": "^0.9.39", "@solana/wallet-adapter-wallets": "^0.19.37", "@solana/web3.js": "^1.98.2", "antd": "^5.25.4", "buffer": "^6.0.3", "crypto-browserify": "^3.12.1", "emotion": "^11.0.0", "framer-motion": "^12.16.0", "i18next": "^25.2.1", "react": "^18.1.0", "react-dom": "^18.1.0", "react-i18next": "^15.5.3", "react-icons": "^5.5.0", "rollup-plugin-node-polyfills": "^0.2.1"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^22.15.30", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}, "packageManager": "yarn@1.22.21+sha1.****************************************"}