body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  min-height: 100vh;
}

.ant-btn-primary {
  background: linear-gradient(90deg, #5618ff 0%, #fb5c5c 100%);
  border: none;
  border-radius: 8px;
  transition: transform 0.2s ease, filter 0.2s ease;
}

.ant-btn-primary:active {
  background: linear-gradient(90deg, #5618ff 0%, #fb5c5c 100%);
  /* Maintain gradient */
  transform: scale(0.95);
  /* Slight scale-down for tactile feedback */
  filter: brightness(0.85);
  /* Subtle darkening on click */
}

.ant-btn-primary:hover {
  background: linear-gradient(90deg, #5618ff 0%, #fb5c5c 100%);
  /* Maintain gradient */
  transform: scale(0.95);
  /* Slight scale-down for tactile feedback */
  filter: brightness(0.85);
  /* Subtle darkening on click */
}

.ant-card {
  color: #ffffff;
  border-radius: 16px;
  /* Slightly larger for a modern look */
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.25) 0%, rgba(255, 255, 255, 0.1) 20%);
  /* Subtle gradient for depth */
  backdrop-filter: blur(30px);
  /* Stronger blur for premium frosted glass */
  border: 1px solid rgba(255, 255, 255, 0.2);
  /* Slightly stronger border */
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.2);
  /* Deeper shadow for 3D effect */
  transition: transform 0.3s ease, backdrop-filter 0.3s ease, box-shadow 0.3s ease;
}

.ant-card:hover {
  transform: translateY(-4px);
  /* Slight lift for interactivity */
  backdrop-filter: blur(35px);
  /* Increase blur on hover */
  box-shadow: 0 12px 32px rgba(0, 0, 0, 0.25);
  /* Stronger shadow on hover */
}

/* Ensure text legibility on glass background */
.ant-card .ant-typography,
.ant-card .ant-statistic-title,
.ant-card .ant-statistic-content {
  color: #ffffff;
  /* White text for contrast */
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
  /* Subtle shadow for readability */
}
