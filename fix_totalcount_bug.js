// 修复 totalCount 为空对象的 bug
// 在浏览器控制台中运行此脚本

console.log('🔧 修复 totalCount 缓存 bug...');

// 检查当前缓存
const currentCache = localStorage.getItem('irys_stats_cache');
console.log('📊 当前缓存:', currentCache);

if (currentCache) {
    try {
        const parsed = JSON.parse(currentCache);
        console.log('📊 解析后:', parsed);
        console.log('📊 totalCount 类型:', typeof parsed.totalCount);
        
        if (typeof parsed.totalCount !== 'number') {
            console.log('❌ 检测到 totalCount 类型错误!');
            console.log('🔧 清除损坏的缓存...');
            localStorage.removeItem('irys_stats_cache');
            console.log('✅ 缓存已清除');
            console.log('🔄 请刷新页面重新获取数据');
        } else {
            console.log('✅ totalCount 类型正确');
        }
    } catch (e) {
        console.log('❌ 缓存解析失败:', e);
        console.log('🔧 清除损坏的缓存...');
        localStorage.removeItem('irys_stats_cache');
        console.log('✅ 缓存已清除');
        console.log('🔄 请刷新页面重新获取数据');
    }
} else {
    console.log('📊 无缓存数据');
}

// 自动刷新页面
console.log('🔄 3秒后自动刷新页面...');
setTimeout(() => {
    window.location.reload();
}, 3000);
