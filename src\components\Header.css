/* 默认样式 */
.header {
  padding: 0 50px;
}

/* 桌面端显示动作区域，隐藏汉堡菜单 */
.desktop-only {
  display: flex !important;
}

.desktop-only a {
  font-weight: 800;
}
.desktop-only a:hover {
  color: #ff6b6b !important;
}

.header-logo {
  display: flex;
  flex-direction: row;
  gap: 4px;
  align-items: center;
  cursor: pointer;
}

.header-logo:hover {
  color: #ff6b6b !important;
}

.ant-select-selector {
  background: transparent !important;
  color: #fff !important;
  border: 1px solid rgba(255, 255, 255, 0.1) !important;
  border-radius: 2px !important;
  height: 45px !important;
}

.ant-select .ant-select-arrow {
  color: #fff !important;
}

.ant-select-selection-item {
  color: #fff !important;
}

.mobile-only {
  display: none !important;
}

/* 移动端适配 */
@media (max-width: 768px) {
  .header {
    flex-direction: row;
    padding: 0 16px;
    height: auto;
    padding-top: 12px;
    padding-bottom: 12px;
  }

  .header .ant-typography {
    font-size: 16px !important;
  }

  .header img {
    height: 28px !important;
    margin-right: 8px;
  }

  /* 隐藏桌面端动作区域 */
  .desktop-only {
    display: none !important;
  }

  /* 显示汉堡菜单按钮 */
  .mobile-only {
    display: block !important;
  }

  /* 抽屉内菜单项样式 */
  .ant-drawer-body .ant-menu-item {
    height: 46px !important;
    /* 设置菜单项高度 */
    line-height: 46px !important;
    /* 垂直居中 */
    padding: 0 16px !important;
    /* 调整内边距 */
    margin: 0 !important;
    /* 移除默认外边距 */
  }

  /* 语言选择器高度 */
  .ant-drawer-body .header-menu-select .ant-select-selector {
    height: 46px !important;
    /* 设置 Select 高度 */
    line-height: 46px !important;
    padding: 0 11px !important;
    /* 调整内边距以垂直居中 */
  }

  /* 钱包按钮高度 */
  .ant-drawer-body .header-menu-wallet .ant-btn {
    height: 46px !important;
    /* 设置 Wallet 按钮高度 */
    line-height: 46px !important;
    width: 100% !important;
    text-align: left !important;
  }
}

@media (max-width: 480px) {
  .header .ant-typography {
    font-size: 14px !important;
  }

  .header img {
    height: 24px !important;
  }
}
