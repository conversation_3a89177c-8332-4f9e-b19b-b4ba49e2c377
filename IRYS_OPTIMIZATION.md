# Irys Stats Optimization

## 概述

StatsPanel 组件中的 Irys 数据获取已经进行了优化，实现了智能缓存和增量更新机制。

## 优化特性

### 1. 智能缓存
- 使用 localStorage 缓存已获取的数据
- 缓存包含：总 DOI 数量、最后游标位置、最后更新时间
- 缓存有效期：5分钟

### 2. 增量更新
- 首次加载：完整扫描所有数据
- 后续更新：仅从上次停止的游标位置继续获取新数据
- 大幅减少 API 调用次数和数据传输量

### 3. 性能优化
- 增量更新时限制最多10页查询（vs 完整扫描的50页）
- 智能停止条件：检测到非完整页面时停止
- 错误恢复：获取失败时使用缓存数据

## 使用方法

### 正常使用
组件会自动处理缓存和增量更新，无需额外配置。

### 开发者工具
在浏览器控制台中可以使用以下命令：

```javascript
// 清除缓存（强制重新获取所有数据）
window.clearIrysCache()
```

### 缓存数据结构
```typescript
interface IrysCacheData {
    totalDois: number;        // 总 DOI 数量
    lastCursor: string | null; // 最后的游标位置
    lastUpdate: number;       // 最后更新时间戳
}
```

## 性能提升

### 首次加载
- 与原版本相同，需要完整扫描

### 后续加载
- 仅获取新增数据
- 减少 80-90% 的 API 调用
- 加载时间从数十秒减少到几秒

### 缓存命中
- 5分钟内的重复访问直接使用缓存
- 加载时间 < 1秒

## 技术实现

### 缓存策略
1. 检查缓存是否存在且有效
2. 如果缓存有效，直接返回缓存数据
3. 如果缓存过期或不存在，执行增量更新
4. 更新缓存数据

### 增量更新逻辑
1. 从缓存的游标位置开始查询
2. 收集新的 DOI 数据
3. 将新数据数量加到缓存的总数上
4. 更新游标和时间戳

### 错误处理
- 网络错误时使用缓存数据
- 解析错误时清除损坏的缓存
- 提供详细的控制台日志

## 监控和调试

### 控制台日志
- `📊 Using cached Irys stats: X` - 使用缓存数据
- `📊 Fetching Irys stats...` - 开始获取数据
- `📄 Page X: Y files, Z new DOIs` - 每页处理进度
- `📊 Irys stats updated: X total DOIs (Y new)` - 更新完成

### 缓存状态检查
```javascript
// 查看当前缓存数据
JSON.parse(localStorage.getItem('irys_stats_cache'))
```

## 注意事项

1. 缓存存储在用户本地，清除浏览器数据会丢失缓存
2. 首次使用仍需要完整扫描，请耐心等待
3. 如果数据异常，可以手动清除缓存重新获取
