import { Card, Table, notification, ConfigProvider, theme, Input, Space } from "antd";
import { WalletOutlined } from "@ant-design/icons";
import { useState, useEffect, useCallback } from "react";
import { useTranslation } from "react-i18next";

interface DonationRecord {
  time: string;
  sender: string;
  amount: string;
  source: string;
  destination: string;
  memo: string;
  signature: string;
}

export const DonationList: React.FC = () => {
  const [donations, setDonations] = useState<DonationRecord[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [query, setQuery] = useState<string>("");
  const { t } = useTranslation();

  // Parse CSV data
  const parseCSV = (csvText: string): DonationRecord[] => {
    const lines = csvText.split("\n");
    const records: DonationRecord[] = [];

    // Skip header line
    for (let i = 1; i < lines.length; i++) {
      const line = lines[i].trim();
      if (!line) continue;

      // Parse CSV line (handle quoted fields)
      const fields: string[] = [];
      let current = "";
      let inQuotes = false;

      for (let j = 0; j < line.length; j++) {
        const char = line[j];
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === "," && !inQuotes) {
          fields.push(current.trim());
          current = "";
        } else {
          current += char;
        }
      }
      fields.push(current.trim());

      if (fields.length >= 7) {
        records.push({
          time: fields[0].replace(/"/g, ""),
          sender: fields[1],
          amount: fields[2],
          source: fields[3],
          destination: fields[4],
          memo: fields[5] || "N/A",
          signature: fields[6],
        });
      }
    }

    return records;
  };

  // Fetch donations from local CSV
  const fetchDonations = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch("/donation_list.csv");
      if (!response.ok) {
        throw new Error("Failed to fetch CSV file");
      }
      const csvText = await response.text();
      const records = parseCSV(csvText);
      setDonations(records);
      setLoading(false);
    } catch (error) {
      console.error(error);
      setLoading(false);
      notification.error({
        message: t("fetch_failed"),
        description: (error as Error).message,
      });
    }
  }, [t]);

  // Table columns for donation list
  const columns = [
    {
      title: t("time"),
      dataIndex: "time",
      key: "time",
      render: (text: string) => <span style={{ color: "#e6f7ff" }}>{text}</span>,
    },
    {
      title: t("sender"),
      dataIndex: "sender",
      key: "sender",
      render: (text: string) => (
        <a href={`https://explorer.solana.com/address/${text}`} target="_blank" rel="noopener noreferrer" style={{ color: "#40c4ff" }}>
          {text.slice(0, 4)}...{text.slice(-4)}
        </a>
      ),
    },
    {
      title: t("amount"),
      dataIndex: "amount",
      key: "amount",
      render: (amount: string) => <span style={{ color: "#52c41a", fontWeight: "bold" }}>{amount}</span>,
    },
    {
      title: t("eth_address"),
      dataIndex: "memo",
      key: "memo",
      render: (text: string) =>
        text !== "N/A" && text ? (
          <a href={`https://bscscan.com/address/${text}`} target="_blank" rel="noopener noreferrer" style={{ color: "#ff7a45" }}>
            {text.slice(0, 6)}...{text.slice(-4)}
          </a>
        ) : (
          <span style={{ color: "#8c8c8c" }}>N/A</span>
        ),
    },
    {
      title: t("tx_signature"),
      dataIndex: "signature",
      key: "signature",
      render: (text: string) => (
        <a href={`https://explorer.solana.com/tx/${text}`} target="_blank" rel="noopener noreferrer" style={{ color: "#722ed1" }}>
          {text.slice(0, 5)}...{text.slice(-5)}
        </a>
      ),
    },
  ];

  // Fetch donations on component mount
  useEffect(() => {
    fetchDonations();
  }, [fetchDonations]);

  const filtered = donations.filter((d) => {
    const q = query.trim().toLowerCase();
    if (!q) return true;
    return d.sender.toLowerCase().includes(q) || d.destination.toLowerCase().includes(q) || (d.memo || "").toLowerCase().includes(q) || d.signature.toLowerCase().includes(q);
  });

  return (
    <ConfigProvider
      theme={{
        algorithm: theme.darkAlgorithm,
        token: {
          colorBgContainer: "#1a1a1a",
          colorBgElevated: "#2c2c2c",
          colorText: "#ffffff",
          colorTextSecondary: "#b3b3b3",
          colorBorder: "rgba(255, 255, 255, 0.1)",
          colorPrimary: "#40c4ff",
        },
      }}
    >
      <Card
        title={
          <span style={{ color: "#fff", fontSize: "24px", fontWeight: "bold" }}>
            <WalletOutlined style={{ marginRight: 8, color: "#40c4ff" }} />
            {t("donation_list")}
          </span>
        }
        style={{
          textAlign: "left",
          marginBottom: 24,
          background: "linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%)",
          borderRadius: 12,
          boxShadow: "0 8px 24px rgba(0, 0, 0, 0.4)",
          border: "1px solid rgba(255, 255, 255, 0.1)",
        }}
        styles={{
          header: {
            background: "transparent",
            borderBottom: "1px solid rgba(255, 255, 255, 0.1)",
          },
          body: {
            background: "transparent",
          },
        }}
      >
        <Space direction="vertical" style={{ width: "100%" }} size={16}>
          <Input.Search placeholder={t("search_placeholder")} allowClear onSearch={setQuery} onChange={(e) => setQuery(e.target.value)} style={{ maxWidth: 600 }} />
          <Table
            dataSource={filtered}
            columns={columns}
            loading={loading}
            rowKey="signature"
            pagination={{
              pageSize: 10,
              showSizeChanger: true,
              showQuickJumper: true,
              showTotal: (total, range) => `${range[0]}-${range[1]} of ${total} items`,
            }}
            style={{
              background: "transparent",
            }}
            scroll={{ x: "max-content" }}
            size="middle"
          />
        </Space>
      </Card>
    </ConfigProvider>
  );
};
