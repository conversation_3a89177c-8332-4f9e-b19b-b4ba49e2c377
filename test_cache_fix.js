// 测试缓存修复的脚本
// 在浏览器控制台中运行

console.log('🔧 开始测试缓存修复...');

// 1. 检查当前缓存状态
function checkCacheStatus() {
    try {
        const cached = localStorage.getItem('irys_stats_cache');
        if (!cached) {
            console.log('📊 缓存为空，将进行完整扫描');
            return null;
        }
        
        const data = JSON.parse(cached);
        console.log(`📊 当前缓存状态:`);
        console.log(`   - DOI数量: ${data.allDoisArray ? data.allDoisArray.length : 'N/A'}`);
        console.log(`   - 最后更新: ${new Date(data.lastUpdate).toLocaleString()}`);
        console.log(`   - 游标: ${data.lastCursor ? data.lastCursor.substring(0, 20) + '...' : 'N/A'}`);
        
        return data;
    } catch (error) {
        console.log('❌ 缓存数据损坏:', error);
        return null;
    }
}

// 2. 清除缓存函数
function forceClearCache() {
    localStorage.removeItem('irys_stats_cache');
    console.log('✅ 缓存已清除');
}

// 3. 运行测试
console.log('\n=== 缓存状态检查 ===');
const currentCache = checkCacheStatus();

if (currentCache && currentCache.allDoisArray && currentCache.allDoisArray.length < 200000) {
    console.log('\n⚠️  检测到缓存数据可能过时');
    console.log('💡 建议清除缓存以获取最新数据');
    console.log('🔧 运行 forceClearCache() 来清除缓存');
} else if (currentCache && currentCache.allDoisArray && currentCache.allDoisArray.length >= 200000) {
    console.log('\n✅ 缓存数据看起来是最新的');
} else {
    console.log('\n📊 无缓存数据，将进行完整扫描');
}

// 4. 提供便捷函数
window.checkCacheStatus = checkCacheStatus;
window.forceClearCache = forceClearCache;

console.log('\n=== 可用命令 ===');
console.log('checkCacheStatus() - 检查缓存状态');
console.log('forceClearCache() - 强制清除缓存');
console.log('window.clearIrysCache() - 使用组件提供的清除函数');

console.log('\n🔧 测试完成！');
